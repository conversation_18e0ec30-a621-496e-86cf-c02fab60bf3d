{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "axios": "^1.10.0", "framer-motion": "^12.19.1", "gsap": "^3.13.0", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "react-parallax-tilt": "^1.7.298", "react-router-dom": "^7.6.2", "three": "^0.177.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "vite": "^7.0.0"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC"}